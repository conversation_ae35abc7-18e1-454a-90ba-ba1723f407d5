<?php

namespace App\Services;

use App\Repositories\CallRepository;

class CallService extends ModelService
{
    public function __construct()
    {
        parent::__construct(new CallRepository());
    }

    public function storeRecording($data)
    {
        $call_id = $data['call_id'];
        $duration = $data['duration'];
        $path = $data['file']->storeAs('recordings', $call_id . '.wav', 'public');

        $call = $this->repository->getCallByCallId($call_id);

        return $this->repository->update($call->id, [
            'status' => 'COMPLETED',
            'record_path' => $path,
            'duration' => $duration,
        ]);
    }

    public function getAllCategorized(): array
    {
        $calls = $this->repository->getAll();
        $categorized = [];

        foreach ($calls as $call) {
            // Enum -> string dönüşüm
            $type = $call->type instanceof \App\Enum\CallType
                ? $call->type->value
                : (string) $call->type;

            $status = $call->status instanceof \App\Enum\CallStatus
                ? $call->status->value
                : (string) $call->status;

            // Ana kategori yoksa <PERSON>
            if (!isset($categorized[$type])) {
                $categorized[$type] = [
                    'missed' => [],
                    'other' => [],
                ];
            }

            // MISSED'leri ayır
            if ($status === \App\Enum\CallStatus::MISSED->value) {
                $categorized[$type]['missed'][] = $call;
            } else {
                $categorized[$type]['other'][] = $call;
            }
        }

        return $categorized;
    }


}