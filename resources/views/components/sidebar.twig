
{% extends 'html.twig' %}

{% block body %}
<script src="{{ asset('js/sip-manager.js') }}"></script>
<link rel="stylesheet" href="{{ asset('css/sidebar/sidebar.css') }}">
<link rel="stylesheet" href="{{ asset('css/calls/calls.css') }}">


<!-- Floating Dialpad Button -->
<button id="showDialpadBtn" class="floating-dialpad-btn">
    <span class="iconify" style="font-size:24px;" data-icon="ic:baseline-dialpad" data-inline="false"></span>
</button>

<!-- Include Dialpad Component -->
{% include 'components/dialpad.twig' %}

<script src="{{ asset('js/console-phone-search.js') }}"></script>


{% set sidebarOpen = sidebarOpen is defined ? sidebarOpen : true %}
{% set activePage = activePage is defined ? activePage : '' %}
<div class="d-flex">
    <nav id="sidebar" class="sidebar {{ sidebarOpen ? '' : 'collapsed' }} p-4">
        <div class="sidebar-header d-flex align-items-center justify-content-between flex-column flex-sm-row">
            <img src="/images/white-logo.svg" alt="Elit Santral Logo" class="sidebar-logo open-logo" style="height: 1.75rem;">
            <img src="/images/collapsed-logo.svg" alt="Elit Santral Logo" class="sidebar-logo collapsed-logo d-none" style="height: 1.75rem;">
            <span class="iconify sidebar-toggle-btn" data-icon="hugeicons:layout-left" data-inline="false" style="cursor:pointer;" id="sidebarToggle"></span>
        </div>
        <ul class="list-unstyled">
            {% if authUser.role == 'agent' %}
            <li>
                 <a href="/" class="nav-link d-flex align-items-center" data-route="home" data-title="Ana Sayfa">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:home-04" data-inline="false"></span>
                    <span class="sidebar-label">Ana Sayfa</span>
                </a>
            </li>
            <li>
                <a href="/cagrilar" class="nav-link d-flex align-items-center" data-route="calls" data-title="Çağrılar">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:calling" data-inline="false"></span>
                    <span class="sidebar-label">Çağrılar</span>
                </a>
            </li>
            <li>
                <a href="/planlanmis-cagrilar" class="nav-link d-flex align-items-center" data-route="planned-calls" data-title="Planlanmış Çağrılar">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:calendar-03" data-inline="false"></span>
                    <span class="sidebar-label">Planlanmış Çağrılar</span>
                </a>
            </li>

            <li>
                <a href="/agent-durumlari" class="nav-link d-flex align-items-center" data-route="agent-status" data-title="Agent Durumları">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:filter-horizontal" data-inline="false"></span>
                    <span class="sidebar-label">Agent Durumları</span>
                </a>
            </li>
            <li class="pb-2">
                <a href="/musteri-listesi" class="nav-link d-flex align-items-center" data-route="customer-list" data-title="Müşteri Listesi">
                    <span class="iconify sidebar-icon" data-icon="solar:users-group-rounded-outline" data-inline="false"></span>
                    <span class="sidebar-label">Müşteri Listesi</span>
                </a>
            </li>
            {% endif %}
            
            <li class="d-flex align-items-center justify-content-between">
                <a href="/profilim" class="nav-link d-flex align-items-center" data-route="profile" data-title="Profilim" style="text-decoration: none;">
                    <div class="d-flex align-items-center">
                        <div class="sidebar-profile">
                            <img src="/images/profile.png" height="28" alt="">
                            <div class="online-status"></div>
                        </div>
                        <span class="profile-name text-white ms-2">Barış Korkmaz</span>
                    </div>
                </a>
                <form method="POST" action="{{ route('logout') }}" style="background: none; border: none; padding: 0; margin: 0;">
                    {{ csrf_field() }}
                    <button type="submit" style="background: none; border: none; padding: 0; margin: 0;">
                        <span class="iconify sidebar-icon" data-icon="akar-icons:sign-out" data-inline="false"></span>
                    </button>
                </form>
            </li>
                        
            

            
            <!-- SIP WebSocket Status -->
            <li class="websocket-status-item">
                <div class="nav-link d-flex align-items-center" style="background: none; padding: 0.5rem 0;">
                    <div class="d-flex align-items-center">
                        <div class="websocket-status-indicator" id="sidebarSipStatus"></div>
                        <span class="websocket-label">SIP Durumu</span>
                    </div>
                </div>
                <!-- WebSocket Status -->
                <div class="nav-link d-flex align-items-center" style="background: none; padding: 0.5rem 0;">
                    <div class="d-flex align-items-center">
                        <div class="websocket-status-indicator" id="websocketStatus"></div>
                        <span class="websocket-label">WebSocket Durumu</span>
                    </div>
                    <div class="websocket-ping-tooltip" id="websocketPing">Ping: -- ms</div>
                </div>
            </li>
        </ul>
    </nav>
    <div class="flex-grow-1 d-flex flex-column main-content-wrapper" id="main-content">
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom py-0" style="overflow-x:hidden;">
            <span class="navbar-brand p-0 me-0">
                {% block page_title %}
                {% endblock %}
            </span>
        </nav>
        <div class="content-wrapper">
            {% block page_content %}
                {% include 'body.twig' %}
            {% endblock %}
        </div>
    </div>
</div>

<!-- AJAX Navigation for Sidebar -->
<script src="{{ asset('js/home.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const toggleBtn = document.getElementById('sidebarToggle');
    const openLogo = document.querySelector('.open-logo');
    const collapsedLogo = document.querySelector('.collapsed-logo');
    const sidebarHeader = document.querySelector('.sidebar-header');

    function setSidebarByWidth() {
        if(window.innerWidth <= 576) {
            sidebar.classList.add('collapsed');
            document.body.classList.add('sidebar-collapsed');
            openLogo.classList.add('d-none');
            collapsedLogo.classList.remove('d-none');
            sidebarHeader.classList.add('flex-column');
            sidebarHeader.classList.remove('flex-md-row');
        } else {
            sidebar.classList.remove('collapsed');
            document.body.classList.remove('sidebar-collapsed');
            openLogo.classList.remove('d-none');
            collapsedLogo.classList.add('d-none');
            sidebarHeader.classList.remove('flex-column');
            sidebarHeader.classList.add('flex-md-row');
        }
    }
    function loadCallHistory() {
    window.axios.get('/api/calls')
        .then(response => {
        const callHistory = response.data;
        const container = document.querySelector('#recentCallsContent .flex-column');

        // Mevcut içeriği temizle (opsiyonel)
        container.innerHTML = '<span class="day-text">Bugün</span>';

        callHistory.forEach(call => {
            // Statüye göre ikon seçimi (örnek)
            let callIcon = '';
            let iconColor = '';
            call.status = call.status.toLowerCase(); // Küçük harfe çevir
            call.type = call.type.toLowerCase(); // Küçük harfe çevir
            
            if (call.status === 'missed') {
            callIcon = 'hugeicons:call-blocked-02';
            } else if (call.type === 'outgoing') {
            callIcon = 'hugeicons:call-outgoing-04';
            } else if (call.type === 'incoming') {
            callIcon = 'hugeicons:call-incoming-04'; // Farklı bir ikon seçmek istersen burada değiştir
            }

            // Süreyi mm:ss formatına çevir
            function formatDuration(secs) {
            if (!secs) return '00:00';
            const m = Math.floor(secs / 60).toString().padStart(2, '0');
            const s = (secs % 60).toString().padStart(2, '0');
            return `${m}:${s}`;
            }

            // Saat bilgisini hh:mm formatına çevir
            function formatTime(iso) {
            const d = new Date(iso);
            return d.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });
            }

            const callDiv = document.createElement('div');
            callDiv.className = 'call-info';
            callDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <span class="iconify" data-icon="${callIcon}" data-inline="false" style="font-size: 1.25rem;"></span>
                <div class="d-flex flex-column ms-3">
                <h6 class="call-name mb-0">${call.caller_id}</h6>
                <span class="call-time d-flex align-items-center mt-1">
                    <span class="iconify" style="font-size: 1rem; color: #444444;" data-icon="lucide:clock-4" data-inline="false"></span>
                    <span class="mx-1" style="font-weight: 600; color: #444444;">${formatTime(call.created_at)}</span>
                </span>
                </div>
            </div>
            <div class="d-flex align-items-center">
                <span class="call-duration me-2" style="color: #94A3B8; font-weight: 500; font-size: 14px;">${formatDuration(call.duration)}</span>
                <span class="iconify call-again-btn" data-icon="fluent:call-outbound-16-filled" data-inline="false"></span>
                
            </div>
            `;
            container.appendChild(callDiv);
        });

        // Eğer hiç kayıt yoksa:
        if (callHistory.length === 0) {
            container.innerHTML += '<div class="text-center">Arama geçmişiniz bulunmamaktadır.</div>';
        }
        })
        .catch(error => {
        console.error('Error loading call history:', error);
        });
    }


    setSidebarByWidth();
    window.addEventListener('resize', setSidebarByWidth);

    toggleBtn.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        document.body.classList.toggle('sidebar-collapsed');
        if (sidebar.classList.contains('collapsed')) {
            openLogo.classList.add('d-none');
            collapsedLogo.classList.remove('d-none');
            sidebarHeader.classList.add('flex-column');
            sidebarHeader.classList.remove('flex-md-row');
        } else {
            openLogo.classList.remove('d-none');
            collapsedLogo.classList.add('d-none');
            sidebarHeader.classList.remove('flex-column');
            sidebarHeader.classList.add('flex-md-row');
        }
        // Sidebar açılıp kapandığında tüm dropdownları kapat
        closeAllDropdownMenus();
    });

    // Sidebar dropdown toggle
    const dropdownToggles = document.querySelectorAll('.sidebar-dropdown-toggle');
    let activeDropend = null;
    let activeDropendToggle = null;
    let dropendLock = false; // Sadece collapsed modda kullanılacak
    function closeAllDropendsCollapsed() {
        dropendLock = true;
        document.querySelectorAll('.sidebar-dropend-popup').forEach(p => p.remove());
        document.querySelectorAll('.sidebar-dropdown.open').forEach(li => {
            li.classList.remove('open');
            const sm = li.querySelector('.sidebar-dropdown-menu');
            if (sm) sm.style.display = 'none';
        });
        activeDropend = null;
        activeDropendToggle = null;
        setTimeout(() => { dropendLock = false; }, 200);
    }

    // Sidebar açılıp kapandığında tüm dropdownları kapat
    function closeAllDropdownMenus() {
        document.querySelectorAll('.sidebar-dropdown.open').forEach(li => {
            li.classList.remove('open');
            const sm = li.querySelector('.sidebar-dropdown-menu');
            if (sm) sm.style.display = 'none';
        });
        // Ayrıca dropend popup'ı da kapat
        document.querySelectorAll('.sidebar-dropend-popup').forEach(p => p.remove());
        activeDropend = null;
        activeDropendToggle = null;
    }

    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const parentLi = this.parentElement;
            const submenu = parentLi.querySelector('.sidebar-dropdown-menu');
            // Eğer sidebar collapsed ise dropend popup aç
            if (sidebar.classList.contains('collapsed')) {
                if (dropendLock) return;
                // Eğer aynı toggle'a tekrar tıklandıysa sadece kapat
                if (activeDropend && activeDropendToggle === this) {
                    closeAllDropendsCollapsed();
                    return;
                }
                // Önce tüm popup ve open class'ları temizle
                closeAllDropendsCollapsed();
                // Menü klonla ve body'ye ekle
                const rect = this.getBoundingClientRect();
                const popup = submenu.cloneNode(true);
                popup.classList.add('sidebar-dropend-popup');
                popup.style.position = 'absolute';
                popup.style.top = (rect.top + window.scrollY) + 'px';
                popup.style.left = (rect.right + 8) + 'px';
                popup.style.display = 'block';
                document.body.appendChild(popup);
                activeDropend = popup;
                activeDropendToggle = this;
                parentLi.classList.add('open');
                setTimeout(() => {
                    document.addEventListener('mousedown', closeDropend, { once: true });
                    document.addEventListener('touchstart', closeDropend, { once: true });
                }, 0);
                const closeOnSidebarOpen = () => {
                    if (!sidebar.classList.contains('collapsed')) {
                        closeAllDropendsCollapsed();
                        document.removeEventListener('transitionend', closeOnSidebarOpen);
                    }
                };
                sidebar.addEventListener('transitionend', closeOnSidebarOpen);
                function closeDropend(ev) {
                    if (activeDropend && !popup.contains(ev.target) && ev.target !== toggle) {
                        closeAllDropendsCollapsed();
                    } else {
                        document.addEventListener('mousedown', closeDropend, { once: true });
                        document.addEventListener('touchstart', closeDropend, { once: true });
                    }
                }
                popup.querySelectorAll('.nav-link').forEach(link => {
                    link.addEventListener('click', () => {
                        closeAllDropendsCollapsed();
                    });
                });
                return;
            }
            // Sidebar açıkken klasik davranış, lock yok
            // Sadece kendi submenu'sunu toggle et, display:none ile uğraşma
            if (parentLi.classList.contains('open')) {
                parentLi.classList.remove('open');
                submenu.style.display = 'none';
            } else {
                document.querySelectorAll('.sidebar-dropdown.open').forEach(li => {
                    li.classList.remove('open');
                    const sm = li.querySelector('.sidebar-dropdown-menu');
                    if (sm) sm.style.display = 'none';
                });
                parentLi.classList.add('open');
                submenu.style.display = 'block';
            }
        });
    });


    loadCallHistory();
    // Aktif route'a göre sidebar'da active class ekle
    var currentPath = window.location.pathname;
    var navLinks = document.querySelectorAll('#sidebar .nav-link[href]');
    navLinks.forEach(function(link) {
        // Sadece path kısmını karşılaştır (query string ve hash hariç)
        var linkPath = link.getAttribute('href');
        if(linkPath === currentPath || (linkPath !== '#' && currentPath.startsWith(linkPath) && linkPath !== '/')) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
});
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
    // Aktif linki bul
    var activeLink = document.querySelector('.sidebar-dropdown-menu a.active');
    if (activeLink) {
        // Dropdown menüsünü aç
        var dropdownMenu = activeLink.closest('.sidebar-dropdown-menu');
        if (dropdownMenu) {
            dropdownMenu.style.display = 'block';
            // Dropdown parent'ına "open" class'ı ekle
            var dropdownParent = dropdownMenu.closest('.sidebar-dropdown');
            if (dropdownParent) {
                dropdownParent.classList.add('open');
            }
        }
        // Aktif linkin arka planını ayarla
        activeLink.style.background = '#3C4257';
        activeLink.style.color = '#fff';
    }
});
</script>





<script>
// Backend websoket bağlantı alanı. NOT: SIP WEBSOKET DEGIL
let websocket = null;
let websocketReconnectInterval = null;
let pingInterval = null;
let lastPingTime = 0;

function connectWebSocket() {
    try {
        // if request url 127.0.0.1
        websocket = new WebSocket('wss://chat.tamyeritamzamani.com:22478');
        
        window.ws = websocket;

        
        websocket.onopen = function() {
            console.log('WebSocket bağlantısı kuruldu');
            updateWebSocketStatus(true);
            clearInterval(websocketReconnectInterval);

            const userId = '{{ authUser.id }}';
            window.currentUserId = userId;
            window.currentCompanyId = '{{ authUser.company_id }}';
            window.currentUserName = '{{ authUser.name }}';
            
            if (userId) {
                websocket.send(JSON.stringify({
                    type: 'auth',
                    companyId: '{{ authUser.company_id }}',
                    userName: '{{ authUser.name }}',
                    userId: userId
                }));
            }
            
            startPingMeasurement();
        };
        
        websocket.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                if (data.type === 'auth_success') {
                    console.log('Kimlik doğrulandı');
                }
                if (data.type === 'agent_status_update') {        
                    if (window.location.pathname === '/agent-durumlari') {
                        updateAgentStatusTable(data.agents);
                    }
                }
            } catch (e) {
                if (event.data === 'pong') {
                    const ping = Date.now() - lastPingTime;
                    updatePingDisplay(ping);
                }
            }
        };
        
        websocket.onclose = function() {
            console.log('WebSocket bağlantısı kapandı');
            updateWebSocketStatus(false);
            stopPingMeasurement();
        };
        
        websocket.onerror = function(error) {
            console.log('WebSocket hata:', error);
            updateWebSocketStatus(false);
            websocketReconnectInterval = setInterval(connectWebSocket, 5000);
        };
        
    } catch (error) {
        console.log('WebSocket bağlantı hatası:', error);
        updateWebSocketStatus(false);
        websocketReconnectInterval = setInterval(connectWebSocket, 5000);
    }
}

function startPingMeasurement() {
    pingInterval = setInterval(() => {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            lastPingTime = Date.now();
            websocket.send('ping');
        }
    }, 5000); // Her 5 saniyede bir ping gönder
}

function stopPingMeasurement() {
    if (pingInterval) {
        clearInterval(pingInterval);
        pingInterval = null;
    }
    updatePingDisplay(null);
}

function updateWebSocketStatus(isConnected) {
    const statusIndicator = document.getElementById('websocketStatus');
    if (statusIndicator) {
        if (isConnected) {
            statusIndicator.classList.add('connected');
        } else {
            statusIndicator.classList.remove('connected');
        }
    }
}

function updatePingDisplay(ping) {
    const pingElement = document.getElementById('websocketPing');
    if (pingElement) {
        if (ping !== null) {
            pingElement.textContent = `Ping: ${ping} ms`;
        } else {
            pingElement.textContent = 'Ping: -- ms';
        }
    }
}
document.addEventListener('DOMContentLoaded', function() {
    clearInterval(websocketReconnectInterval); // yeni
    connectWebSocket();
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const showDialpadBtn = document.getElementById('showDialpadBtn');
    const dialpadPanel = document.getElementById('dialpadPanel');

    // Make showDialpad function globally available
    window.showDialpad = function() {
        dialpadPanel.style.display = 'block';
        dialpadPanel.classList.add('show');
        showDialpadBtn.style.display = 'none';

        // Trigger SIP initialization if available
        if (typeof window.initializeSIPIfNeeded === 'function') {
            window.initializeSIPIfNeeded();
        }
    };

    showDialpadBtn.addEventListener('click', function() {
        window.showDialpad();
    });
});

// Global function to update agent status table
function updateAgentStatusTable(agentData) {
    const table = document.getElementById('agentStatusTable');
    if (!table) {
        console.log('Agent status table not found');
        return;
    }

    console.log('Updating agent status table with data:', agentData);

    // Convert agent data to a map for easier lookup
    const agentMap = new Map();

    // Handle both old format (array of IDs) and new format (array of objects)
    if (Array.isArray(agentData)) {
        agentData.forEach(agent => {
            if (typeof agent === 'string' || typeof agent === 'number') {
                // Old format: just user IDs
                agentMap.set(agent.toString(), {
                    userId: agent.toString(),
                    userName: 'Unknown',
                    callState: 'off_call',
                    isOnline: true
                });
            } else if (typeof agent === 'object' && agent.userId) {
                // New format: agent objects with properties
                agentMap.set(agent.userId.toString(), {
                    userId: agent.userId.toString(),
                    userName: agent.userName || 'Unknown',
                    callState: agent.callState || 'off_call',
                    lastSeen: agent.lastSeen,
                    connectionTime: agent.connectionTime,
                    isOnline: true
                });
            }
        });
    }

    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        // Try different methods to find the agent ID
        let agentId = null;

        // Method 1: First cell (for user agent status table)
        const firstCell = row.querySelector('td:first-child');
        if (firstCell && firstCell.textContent.trim()) {
            agentId = firstCell.textContent.trim();
        }

        // Method 2: Look for a cell that contains a numeric ID
        if (!agentId) {
            const cells = row.querySelectorAll('td');
            for (let cell of cells) {
                const text = cell.textContent.trim();
                if (/^\d+$/.test(text)) {
                    agentId = text;
                    break;
                }
            }
        }

        console.log('Processing row for agent ID:', agentId);

        if (agentId) {
            const agent = agentMap.get(agentId);

            // Find status badges in the row
            const statusBadges = row.querySelectorAll('.status-badge');

            // Update activity status (Panel Durumu) - usually the first status badge
            const activityStatusBadge = statusBadges[0];
            if (activityStatusBadge) {
                if (agent && agent.isOnline) {
                    activityStatusBadge.classList.remove('deactive');
                    activityStatusBadge.classList.add('active');
                    activityStatusBadge.textContent = 'Çevrimiçi';
                } else {
                    activityStatusBadge.classList.remove('active');
                    activityStatusBadge.classList.add('deactive');
                    activityStatusBadge.textContent = 'Çevrimdışı';
                }
            }

            // Update call status (Çağrı Durumu) - usually the second status badge
            const callStatusBadge = statusBadges[1];
            if (callStatusBadge && agent && agent.isOnline) {
                // Update call status based on callState
                if (agent.callState === 'on_call') {
                    callStatusBadge.classList.remove('free', 'deactive');
                    callStatusBadge.classList.add('active');
                    callStatusBadge.textContent = 'Çağrıda';
                } else {
                    callStatusBadge.classList.remove('active', 'deactive');
                    callStatusBadge.classList.add('free');
                    callStatusBadge.textContent = 'Boşta';
                }
            } else if (callStatusBadge && (!agent || !agent.isOnline)) {
                // Agent is offline, set call status to inactive
                callStatusBadge.classList.remove('active', 'deactive');
                callStatusBadge.classList.add('free');
                callStatusBadge.textContent = 'Boşta';
            }

            console.log(`Updated agent ${agentId}:`, {
                isOnline: agent?.isOnline,
                callState: agent?.callState,
                activityStatus: activityStatusBadge?.textContent,
                callStatus: callStatusBadge?.textContent
            });
        }
    });

    console.log('Agent status table update completed');
}
</script>
<script>
    window.sipConfig = {
        uri: '{{ sip_uri }}',
        authorizationUsername: '{{ authUser.internal_number }}',
        authorizationPassword: '{{ authUser.sip_pass }}',
    };
</script>


{% endblock %}

