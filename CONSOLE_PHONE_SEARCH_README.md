# 📞 Console Phone Search Utility

A JavaScript utility that allows you to programmatically fill phone input fields and trigger search/filter functionality from the browser console.

## 🚀 Quick Start

### 1. Include the Script

Add the script to your page:

```html
<script src="/js/console-phone-search.js"></script>
```

### 2. Use from Console

Open browser console (F12) and use:

```javascript
// Fill phone input and trigger search
callPhone("1234567890")

// Alternative function names
searchPhone("************")
fillPhoneAndSearch("(*************")
```

## 📋 Available Functions

### `callPhone(phoneNumber, options)`
Main function that fills phone input and triggers search.

```javascript
callPhone("1234567890")
callPhone("************", { verbose: true })
```

### `searchPhone(phoneNumber, options)`
Alias for `callPhone()` - same functionality.

### `fillPhoneAndSearch(phoneNumber, options)`
Basic version without DataTable integration.

### `showPhoneSearchElements()`
Shows available phone inputs and filter buttons on the current page.

```javascript
showPhoneSearchElements()
```

## ⚙️ Configuration Options

```javascript
callPhone("1234567890", {
    // Show detailed console logs
    verbose: true,
    
    // Delay before clicking filter button (ms)
    delay: 100,
    
    // Also fill modal phone inputs
    triggerModal: true,
    
    // Custom phone input selectors
    phoneInputSelectors: [
        '#internalNo',
        '#phoneNo',
        'input[placeholder*="Telefon"]'
    ],
    
    // Custom filter button selectors
    filterButtonSelectors: [
        '#filterButton',
        'button:contains("Filtrele")',
        '#applyFilters'
    ]
})
```

## 🎯 Supported Elements

### Phone Input Fields
The utility automatically detects these phone input fields:

- `#internalNo` - Desktop phone input (user customer list)
- `#phoneNo` - Admin phone input  
- `#modalCaller` - Modal phone input
- `input[name="internalNo"]`
- `input[name="phoneNo"]`
- `input[placeholder*="Telefon"]` - Any input with "Telefon" in placeholder
- `input[placeholder*="telefon"]` - Case insensitive

### Filter Buttons
Automatically finds and clicks these filter buttons:

- `#filterButton` - Main filter button
- `#applyFilters` - Modal apply filters button
- Any button containing "Filtrele" text
- Any button containing "Filter" text
- Any button containing "Search" text

## 🔧 Features

- **Smart Detection**: Automatically finds phone inputs and filter buttons
- **Phone Number Cleaning**: Removes spaces, dashes, parentheses automatically
- **Error Handling**: Clear error messages when elements aren't found
- **Modal Support**: Can fill both main form and modal inputs
- **DataTable Integration**: Triggers table reloads when available
- **Customizable**: Override selectors and behavior with options
- **Verbose Logging**: Optional detailed console output for debugging

## 📱 Phone Number Formats

The utility accepts various phone number formats:

```javascript
callPhone("1234567890")        // Basic digits
callPhone("************")      // With dashes
callPhone("(*************")    // With parentheses
callPhone("************")      // With spaces
```

All formats are automatically cleaned to digits only.

## 🧪 Testing

### Test on Demo Page

1. Open `console-phone-search-demo.html` in your browser
2. Open console (F12)
3. Try: `callPhone("1234567890")`
4. Watch the phone input get filled and filter button clicked

### Test on Your Application

1. Navigate to your customer list page
2. Open console (F12)
3. Run: `showPhoneSearchElements()` to see available elements
4. Try: `callPhone("1234567890")`

## 🔍 Troubleshooting

### Phone Input Not Found
```javascript
// Check what inputs are available
showPhoneSearchElements()

// Use custom selector
callPhone("1234567890", {
    phoneInputSelectors: ['#yourPhoneInputId']
})
```

### Filter Button Not Found
```javascript
// Check what buttons are available
showPhoneSearchElements()

// Use custom selector
callPhone("1234567890", {
    filterButtonSelectors: ['#yourFilterButtonId']
})
```

### Enable Verbose Logging
```javascript
callPhone("1234567890", { verbose: true })
```

## 🎯 Use Cases

### Customer Service
```javascript
// Quickly search for a customer by phone
callPhone("5551234567")
```

### Testing
```javascript
// Test search functionality with various numbers
["1234567890", "5551234567", "9876543210"].forEach(num => {
    setTimeout(() => callPhone(num), 1000)
})
```

### Automation
```javascript
// Search multiple numbers with delay
const phoneNumbers = ["1111111111", "2222222222", "3333333333"]
phoneNumbers.forEach((phone, index) => {
    setTimeout(() => callPhone(phone), index * 2000)
})
```

## 🔧 Integration with Existing Code

The utility is designed to work with your existing customer list functionality:

1. **Non-Intrusive**: Doesn't modify existing code
2. **Event Compatible**: Triggers proper input/change events
3. **Framework Agnostic**: Works with jQuery, vanilla JS, etc.
4. **DataTable Ready**: Integrates with DataTables if present

## 📝 Console Output Examples

### Successful Search
```
🔍 Searching for phone number: 1234567890
📱 Found phone input: #internalNo
✅ Phone input filled with: 1234567890
🔘 Found filter button: #filterButton
🚀 Filter button clicked - search triggered!
```

### Error Cases
```
❌ Error in phone search: Phone input field not found. Available selectors checked: #internalNo, #phoneNo, #modalCaller
```

## 🚀 Advanced Usage

### Custom Implementation
```javascript
// Create your own search function
function myPhoneSearch(phone) {
    return fillPhoneAndSearch(phone, {
        phoneInputSelectors: ['#myCustomInput'],
        filterButtonSelectors: ['#myCustomButton'],
        verbose: true,
        delay: 200
    })
}
```

### Integration with Other Scripts
```javascript
// Use in your existing scripts
if (window.callPhone) {
    callPhone(customerPhoneNumber)
} else {
    console.warn('Phone search utility not loaded')
}
```

## 📄 License

This utility is provided as-is for internal use. Feel free to modify and extend as needed.

---

**Happy Searching! 📞✨**
