<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Console Phone Search Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .demo-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .phone-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        .filter-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .filter-btn:hover {
            background: #0056b3;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>📞 Console Phone Search Utility</h1>
    
    <p>This utility allows you to programmatically fill phone input fields and trigger search functionality from the browser console.</p>

    <div class="demo-section">
        <h2>Demo Form</h2>
        <p>Try the console functions with this demo form:</p>
        
        <input type="text" id="internalNo" name="internalNo" placeholder="Telefon" class="phone-input">
        <input type="text" id="phoneNo" name="phoneNo" placeholder="Telefon No ile ara..." class="phone-input">
        <br>
        <button id="filterButton" class="filter-btn" onclick="handleFilter()">Filtrele</button>
        <button class="filter-btn" onclick="showResults()">Show Results</button>
    </div>

    <div class="demo-section">
        <h2>🚀 How to Use</h2>
        
        <h3>1. Include the Script</h3>
        <p>Add this script tag to your page:</p>
        <div class="code-block">
&lt;script src="/js/console-phone-search.js"&gt;&lt;/script&gt;
        </div>

        <h3>2. Use from Console</h3>
        <p>Open browser console (F12) and use these commands:</p>
        
        <div class="code-block">
// Basic usage - fill phone input and trigger search
callPhone("1234567890")

// Alternative function names
searchPhone("************")
fillPhoneAndSearch("(*************")

// Show available elements on the page
showPhoneSearchElements()
        </div>

        <h3>3. Advanced Usage</h3>
        <div class="code-block">
// With custom options
callPhone("1234567890", {
    verbose: true,           // Show console logs
    delay: 200,             // Delay before clicking button (ms)
    triggerModal: false     // Don't trigger modal search
})

// Custom selectors
callPhone("1234567890", {
    phoneInputSelectors: ['#myPhoneInput'],
    filterButtonSelectors: ['#myFilterButton']
})
        </div>
    </div>

    <div class="demo-section">
        <h2>🔧 Features</h2>
        <ul>
            <li><strong>Smart Input Detection:</strong> Automatically finds phone input fields using multiple selectors</li>
            <li><strong>Button Auto-Detection:</strong> Finds and clicks filter/search buttons automatically</li>
            <li><strong>Error Handling:</strong> Provides clear error messages if elements aren't found</li>
            <li><strong>Phone Number Cleaning:</strong> Automatically removes spaces, dashes, and parentheses</li>
            <li><strong>Modal Support:</strong> Can also fill modal phone inputs if available</li>
            <li><strong>Verbose Logging:</strong> Optional detailed console output for debugging</li>
            <li><strong>Customizable:</strong> Override selectors and behavior with options</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>📋 Supported Selectors</h2>
        
        <h3>Phone Input Fields:</h3>
        <ul>
            <li><code>#internalNo</code> - Desktop phone input (user customer list)</li>
            <li><code>#phoneNo</code> - Admin phone input</li>
            <li><code>#modalCaller</code> - Modal phone input</li>
            <li><code>input[placeholder*="Telefon"]</code> - Any input with "Telefon" in placeholder</li>
        </ul>

        <h3>Filter Buttons:</h3>
        <ul>
            <li><code>#filterButton</code> - Main filter button</li>
            <li><code>#applyFilters</code> - Modal apply button</li>
            <li>Any button containing "Filtrele" text</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>🧪 Test It Now</h2>
        <p>Open your browser console (F12) and try:</p>
        <div class="code-block">
callPhone("1234567890")
        </div>
        <p>You should see the phone input above get filled and the filter button get clicked!</p>
    </div>

    <div id="results" style="margin-top: 20px;"></div>

    <script src="public/js/console-phone-search.js"></script>
    
    <script>
        function handleFilter() {
            const phoneInput = document.querySelector('#internalNo, #phoneNo');
            const phoneNumber = phoneInput ? phoneInput.value : '';
            
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="demo-section">
                    <h3 class="success">✅ Filter Triggered!</h3>
                    <p><strong>Phone Number:</strong> ${phoneNumber || 'No phone number entered'}</p>
                    <p><strong>Action:</strong> Filter button clicked</p>
                    <p><em>In a real application, this would trigger an AJAX request to filter customer data.</em></p>
                </div>
            `;
        }

        function showResults() {
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="demo-section">
                    <h3>📊 Available Elements</h3>
                    <p>Check the console for detailed information about available phone inputs and buttons.</p>
                </div>
            `;
            showPhoneSearchElements();
        }

        // Show initial message
        console.log('🎉 Demo page loaded! Try: callPhone("1234567890")');
    </script>
</body>
</html>
