/**
 * Console Phone Search Utility
 * Allows programmatic phone number search from browser console
 * 
 * Usage from console:
 * - call<PERSON><PERSON>("1234567890") - Fill phone input and trigger search
 * - search<PERSON>hone("1234567890") - Alternative function name
 * - fillPhoneAndSearch("1234567890") - Descriptive function name
 */

(function() {
    'use strict';

    /**
     * Main function to fill phone input and trigger search
     * @param {string} phoneNumber - The phone number to search for
     * @param {Object} options - Optional configuration
     * @returns {boolean} - Success status
     */
    function fillPhoneAndTriggerSearch(phoneNumber, options = {}) {
        const config = {
            // Default selectors for phone input fields
            phoneInputSelectors: [
                '#internalNo',           // Desktop phone input (user customer list)
                '#phoneNo',              // Admin phone input
                '#modalCaller',          // Modal phone input
                'input[name="internalNo"]',
                'input[name="phoneNo"]',
                'input[placeholder*="Telefon"]',
                'input[placeholder*="telefon"]'
            ],
            // Default selectors for filter/search buttons
            filterButtonSelectors: [
                '#filterButton',
                '.blue-btn:contains("Filtrele")',
                'button:contains("Filtrele")',
                '.filter-btn',
                '#applyFilters'
            ],
            // Whether to also trigger modal search if available
            triggerModal: true,
            // Delay between filling input and clicking button (ms)
            delay: 100,
            // Whether to show console logs
            verbose: true,
            ...options
        };

        try {
            // Validate phone number
            if (!phoneNumber || typeof phoneNumber !== 'string') {
                throw new Error('Phone number must be a non-empty string');
            }

            // Clean phone number (remove spaces, dashes, parentheses)
            const cleanPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');
            
            if (!/^\d+$/.test(cleanPhone)) {
                throw new Error('Phone number must contain only digits (spaces, dashes, parentheses are allowed)');
            }

            if (config.verbose) {
                console.log(`🔍 Searching for phone number: ${cleanPhone}`);
            }

            // Find phone input field
            let phoneInput = null;
            for (const selector of config.phoneInputSelectors) {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) { // Check if visible
                    phoneInput = element;
                    if (config.verbose) {
                        console.log(`📱 Found phone input: ${selector}`);
                    }
                    break;
                }
            }

            if (!phoneInput) {
                throw new Error('Phone input field not found. Available selectors checked: ' + 
                    config.phoneInputSelectors.join(', '));
            }

            // Fill the phone input
            phoneInput.value = cleanPhone;
            phoneInput.dispatchEvent(new Event('input', { bubbles: true }));
            phoneInput.dispatchEvent(new Event('change', { bubbles: true }));

            if (config.verbose) {
                console.log(`✅ Phone input filled with: ${cleanPhone}`);
            }

            // Find and click filter button
            const triggerSearch = () => {
                let filterButton = null;
                
                // Try to find filter button
                for (const selector of config.filterButtonSelectors) {
                    let element;
                    if (selector.includes(':contains')) {
                        // Handle jQuery-style :contains selector manually
                        const text = selector.match(/:contains\("([^"]+)"\)/)?.[1];
                        if (text) {
                            const buttons = document.querySelectorAll('button');
                            element = Array.from(buttons).find(btn => 
                                btn.textContent.trim().includes(text) && 
                                btn.offsetParent !== null
                            );
                        }
                    } else {
                        element = document.querySelector(selector);
                    }
                    
                    if (element && element.offsetParent !== null) { // Check if visible
                        filterButton = element;
                        if (config.verbose) {
                            console.log(`🔘 Found filter button: ${selector}`);
                        }
                        break;
                    }
                }

                if (!filterButton) {
                    console.warn('⚠️ Filter button not found. Available selectors checked: ' + 
                        config.filterButtonSelectors.join(', '));
                    console.log('📋 You may need to manually click the filter button or press Enter');
                    return false;
                }

                // Click the filter button
                filterButton.click();
                
                if (config.verbose) {
                    console.log('🚀 Filter button clicked - search triggered!');
                }

                return true;
            };

            // Trigger search after a short delay to ensure input is processed
            setTimeout(triggerSearch, config.delay);

            // Also try to trigger modal search if enabled
            if (config.triggerModal) {
                setTimeout(() => {
                    const modalPhoneInput = document.querySelector('#phoneNo, #modalCaller');
                    if (modalPhoneInput && modalPhoneInput !== phoneInput) {
                        modalPhoneInput.value = cleanPhone;
                        modalPhoneInput.dispatchEvent(new Event('input', { bubbles: true }));
                        modalPhoneInput.dispatchEvent(new Event('change', { bubbles: true }));
                        
                        const applyButton = document.querySelector('#applyFilters');
                        if (applyButton) {
                            if (config.verbose) {
                                console.log('📋 Also filled modal phone input and ready to apply filters');
                            }
                        }
                    }
                }, config.delay + 50);
            }

            return true;

        } catch (error) {
            console.error('❌ Error in phone search:', error.message);
            return false;
        }
    }

    /**
     * Enhanced search function that also tries to trigger DataTable search
     */
    function enhancedPhoneSearch(phoneNumber, options = {}) {
        const success = fillPhoneAndTriggerSearch(phoneNumber, options);
        
        // Also try to trigger DataTable search if available
        setTimeout(() => {
            if (window.$ && window.$.fn.DataTable) {
                const tables = window.$.fn.DataTable.tables();
                if (tables.length > 0) {
                    // Try to trigger table redraw/search
                    tables.forEach(table => {
                        const api = window.$(table).DataTable();
                        if (api && typeof api.ajax === 'object' && api.ajax.reload) {
                            api.ajax.reload();
                            console.log('🔄 DataTable reloaded');
                        }
                    });
                }
            }
        }, 200);

        return success;
    }

    /**
     * Utility function to show available phone inputs and buttons on the page
     */
    function showAvailableElements() {
        console.log('📋 Available phone input fields:');
        const phoneInputs = document.querySelectorAll('input[type="text"], input[type="tel"]');
        phoneInputs.forEach((input, index) => {
            if (input.placeholder && (
                input.placeholder.toLowerCase().includes('telefon') ||
                input.placeholder.toLowerCase().includes('phone') ||
                input.id.toLowerCase().includes('phone') ||
                input.name.toLowerCase().includes('phone')
            )) {
                console.log(`  ${index + 1}. ${input.tagName}#${input.id || 'no-id'} - "${input.placeholder}"`);
            }
        });

        console.log('\n🔘 Available filter/search buttons:');
        const buttons = document.querySelectorAll('button');
        buttons.forEach((button, index) => {
            const text = button.textContent.trim();
            if (text.includes('Filtrele') || text.includes('Filter') || text.includes('Search') || text.includes('Ara')) {
                console.log(`  ${index + 1}. ${button.tagName}#${button.id || 'no-id'} - "${text}"`);
            }
        });
    }

    // Expose functions to global scope for console access
    window.callPhone = enhancedPhoneSearch;
    window.searchPhone = enhancedPhoneSearch;
    window.fillPhoneAndSearch = fillPhoneAndTriggerSearch;
    window.showPhoneSearchElements = showAvailableElements;

    // Log availability message
    console.log('📞 Phone search functions loaded! Available commands:');
    console.log('  • callPhone("1234567890") - Fill phone input and trigger search');
    console.log('  • searchPhone("1234567890") - Same as callPhone');
    console.log('  • fillPhoneAndSearch("1234567890") - Basic version');
    console.log('  • showPhoneSearchElements() - Show available inputs and buttons');

})();
